{"name": "znotes", "version": "1.0.0", "description": "Mac风格桌面便签应用", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-mac": "electron-builder --mac", "dist": "npm run build", "pack": "electron-builder --dir", "postinstall": "electron-builder install-app-deps"}, "keywords": ["notes", "sticky-notes", "mac", "desktop", "electron"], "author": "zNotes Team", "license": "MIT", "devDependencies": {"electron": "^27.0.0"}, "dependencies": {"electron-store": "^8.1.0"}, "build": {"appId": "com.znotes.app", "productName": "zNotes", "directories": {"output": "dist"}, "files": ["main.js", "renderer/**/*", "assets/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.productivity", "icon": "assets/icon.icns", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "darkModeSupport": true, "hardenedRuntime": true, "entitlements": "assets/entitlements.mac.plist", "entitlementsInherit": "assets/entitlements.mac.plist"}, "dmg": {"title": "zNotes ${version}", "icon": "assets/icon.icns", "background": "assets/dmg-background.png", "contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}]}}, "repository": {"type": "git", "url": "https://github.com/znotes/znotes.git"}, "homepage": "https://github.com/znotes/znotes"}