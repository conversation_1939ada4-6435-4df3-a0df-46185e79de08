# zNotes 桌面应用演示指南

## 桌面应用特性

### 🖥️ 原生桌面体验
- **独立应用窗口**: 真正的桌面应用，不依赖浏览器
- **系统托盘集成**: 最小化到系统托盘，随时快速访问
- **原生菜单栏**: 完整的Mac风格菜单系统
- **窗口管理**: 支持最小化、关闭、全屏等操作

### ⚡ 全局快捷键
- `Cmd/Ctrl + Shift + N`: 快速显示/隐藏应用窗口
- `Cmd/Ctrl + Alt + N`: 全局快速创建新便签

## 功能演示

### 1. 基础操作
- **创建便签**: 点击左上角的 "+" 按钮或使用快捷键 `Cmd+N`
- **编辑便签**: 点击便签列表中的任意便签开始编辑
- **保存便签**: 内容会自动保存，无需手动操作
- **删除便签**: 点击编辑器右上角的删除按钮或使用快捷键 `Cmd+D`

### 2. 搜索功能
- 在搜索框中输入关键词
- 支持搜索便签标题和内容
- 搜索结果会实时更新
- 关键词会在结果中高亮显示

### 3. 主题切换
- 点击左上角的主题切换按钮
- 支持三种模式：自动、浅色、深色
- 自动模式会跟随系统主题设置

### 4. 导出功能
- 点击编辑器中的导出按钮
- 便签会以Markdown格式导出
- 包含标题、内容和时间戳信息

### 5. 快捷键
- `Cmd+N` / `Ctrl+N`: 创建新便签
- `Cmd+D` / `Ctrl+D`: 删除当前便签
- `Cmd+F` / `Ctrl+F`: 聚焦搜索框
- `Esc`: 关闭对话框

## 技术特性

### Mac风格设计
- 采用苹果设计语言
- 流畅的动画效果
- 优雅的界面布局
- 响应式设计

### 数据持久化
- 使用localStorage本地存储
- 数据自动保存和恢复
- 支持大量便签存储

### 用户体验优化
- 实时搜索和过滤
- 自动保存编辑内容
- 字数统计显示
- 时间戳记录

## 使用建议

1. **组织便签**: 使用有意义的标题来组织你的便签
2. **搜索技巧**: 使用关键词快速找到需要的便签
3. **定期导出**: 定期导出重要便签作为备份
4. **主题选择**: 根据使用环境选择合适的主题

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 开始使用

1. 在浏览器中打开 `index.html`
2. 点击"创建便签"开始使用
3. 享受高效的便签管理体验！
