# zNotes - Mac风格桌面便签

一个基于Web技术开发的Mac风格桌面便签应用，在Mac原生便签的基础上增加了便签命名和快速搜索功能。

## 功能特性

### 🎨 Mac风格设计
- 采用Mac系统设计语言
- 优雅的界面和流畅的动画效果
- 支持浅色主题
- 响应式设计，适配不同屏幕尺寸

### 📝 便签管理
- **便签命名**: 为每个便签设置自定义标题
- **实时编辑**: 支持实时编辑便签内容
- **自动保存**: 编辑内容自动保存，无需手动操作
- **字数统计**: 实时显示便签字数
- **时间戳**: 显示便签创建和修改时间

### 🔍 快速搜索
- **实时搜索**: 输入关键词即时显示搜索结果
- **全文搜索**: 搜索便签标题和内容
- **高亮显示**: 搜索结果中关键词高亮显示
- **快速清除**: 一键清除搜索条件

### 💾 数据持久化
- 使用浏览器本地存储(localStorage)
- 数据自动保存，刷新页面不丢失
- 支持导入导出功能(计划中)

### ⌨️ 快捷键支持
- `Cmd/Ctrl + N`: 创建新便签
- `Cmd/Ctrl + D`: 删除当前便签
- `Cmd/Ctrl + F`: 聚焦搜索框
- `Esc`: 关闭对话框

## 使用方法

### 安装和运行
1. 克隆或下载项目文件
2. 在浏览器中打开 `index.html` 文件
3. 开始使用便签应用

### 基本操作
1. **创建便签**: 点击左上角的"+"按钮或使用快捷键 `Cmd+N`
2. **编辑便签**: 点击便签列表中的任意便签开始编辑
3. **搜索便签**: 在搜索框中输入关键词进行搜索
4. **删除便签**: 在编辑界面点击删除按钮或使用快捷键 `Cmd+D`

## 项目结构

```
zNotes/
├── index.html          # 主页面文件
├── styles.css          # 样式文件
├── script.js           # JavaScript逻辑文件
└── README.md           # 项目说明文档
```

## 技术栈

- **HTML5**: 页面结构
- **CSS3**: 样式设计和动画效果
- **JavaScript (ES6+)**: 应用逻辑和交互
- **localStorage**: 数据持久化存储

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 开发计划

### 已完成功能
- [x] 基础UI界面设计
- [x] 便签的创建、编辑、删除
- [x] 便签命名功能
- [x] 实时搜索功能
- [x] 数据本地存储
- [x] 快捷键支持

### 计划中功能
- [ ] 便签分类和标签
- [ ] 便签导入导出
- [ ] 主题切换(深色模式)
- [ ] 便签拖拽排序
- [ ] 富文本编辑
- [ ] 便签同步功能
- [ ] 桌面通知

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

MIT License

## 更新日志

### v1.0.0 (2024-12-15)
- 初始版本发布
- 实现基础便签功能
- 添加搜索和命名功能
- Mac风格UI设计
