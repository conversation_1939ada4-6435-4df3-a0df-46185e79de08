# zNotes - Mac风格桌面便签

一个基于Electron开发的Mac风格桌面便签应用，在Mac原生便签的基础上增加了便签命名和快速搜索功能。提供真正的桌面应用体验，包括系统托盘、全局快捷键、原生菜单等功能。

## 功能特性

### 🎨 Mac风格设计
- 采用Mac系统设计语言
- 优雅的界面和流畅的动画效果
- 支持浅色主题
- 响应式设计，适配不同屏幕尺寸

### 📝 便签管理
- **便签命名**: 为每个便签设置自定义标题
- **实时编辑**: 支持实时编辑便签内容
- **自动保存**: 编辑内容自动保存，无需手动操作
- **字数统计**: 实时显示便签字数
- **时间戳**: 显示便签创建和修改时间

### 🔍 快速搜索
- **实时搜索**: 输入关键词即时显示搜索结果
- **全文搜索**: 搜索便签标题和内容
- **高亮显示**: 搜索结果中关键词高亮显示
- **快速清除**: 一键清除搜索条件

### 💾 数据持久化
- 使用Electron Store进行本地数据存储
- 数据自动保存，应用重启不丢失
- 支持便签导出为Markdown格式

### 🖥️ 桌面应用特性
- **系统托盘**: 最小化到系统托盘，快速访问
- **全局快捷键**:
  - `Cmd/Ctrl + Shift + N`: 快速显示/隐藏应用
  - `Cmd/Ctrl + Alt + N`: 快速创建新便签
- **原生菜单**: 完整的Mac风格菜单栏
- **窗口管理**: 支持最小化、关闭等窗口操作

### ⌨️ 应用内快捷键
- `Cmd/Ctrl + N`: 创建新便签
- `Cmd/Ctrl + D`: 删除当前便签
- `Cmd/Ctrl + F`: 聚焦搜索框
- `Cmd/Ctrl + E`: 导出当前便签
- `Cmd/Ctrl + Shift + E`: 导出所有便签
- `Cmd/Ctrl + T`: 切换主题
- `Cmd/Ctrl + ,`: 打开偏好设置
- `Esc`: 关闭对话框

## 使用方法

### 系统要求
- macOS 10.14+ (推荐)
- Windows 10+
- Linux (Ubuntu 18.04+)
- Node.js 16.0+

### 安装和运行

#### 方式一：直接运行（推荐）
1. 确保已安装 Node.js (https://nodejs.org/)
2. 克隆或下载项目文件
3. 在终端中进入项目目录
4. 运行启动脚本：
   ```bash
   ./start.sh
   ```
   或者手动运行：
   ```bash
   npm install
   npm start
   ```

#### 方式二：开发模式
```bash
npm install
npm run dev
```

### 基本操作
1. **创建便签**:
   - 点击左上角的"+"按钮
   - 使用快捷键 `Cmd+N`
   - 使用全局快捷键 `Cmd+Alt+N`
   - 通过系统托盘菜单
2. **编辑便签**: 点击便签列表中的任意便签开始编辑
3. **搜索便签**: 在搜索框中输入关键词进行搜索
4. **删除便签**: 在编辑界面点击删除按钮或使用快捷键 `Cmd+D`
5. **导出便签**: 使用菜单栏或快捷键导出单个或所有便签
6. **系统托盘**: 应用可最小化到系统托盘，点击托盘图标快速访问

## 项目结构

```
zNotes/
├── main.js                 # Electron主进程文件
├── package.json            # 项目配置和依赖
├── start.sh               # 启动脚本
├── renderer/              # 渲染进程文件
│   ├── index.html         # 主页面文件
│   ├── styles.css         # 样式文件
│   ├── script.js          # 应用逻辑文件
│   └── preload.js         # 预加载脚本
├── assets/                # 资源文件
│   └── entitlements.mac.plist  # Mac应用权限配置
├── README.md              # 项目说明文档
└── demo.md               # 演示指南
```

## 技术栈

- **Electron**: 跨平台桌面应用框架
- **HTML5**: 页面结构
- **CSS3**: 样式设计和动画效果
- **JavaScript (ES6+)**: 应用逻辑和交互
- **Electron Store**: 数据持久化存储
- **Node.js**: 运行时环境

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 开发计划

### 已完成功能
- [x] 基础UI界面设计
- [x] 便签的创建、编辑、删除
- [x] 便签命名功能
- [x] 实时搜索功能
- [x] 数据本地存储
- [x] 快捷键支持

### 计划中功能
- [ ] 便签分类和标签
- [ ] 便签导入导出
- [ ] 主题切换(深色模式)
- [ ] 便签拖拽排序
- [ ] 富文本编辑
- [ ] 便签同步功能
- [ ] 桌面通知

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

MIT License

## 更新日志

### v1.0.0 (2024-12-15)
- 初始版本发布
- 实现基础便签功能
- 添加搜索和命名功能
- Mac风格UI设计
