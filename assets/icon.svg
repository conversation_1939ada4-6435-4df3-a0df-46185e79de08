<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景圆角矩形 -->
  <rect x="32" y="32" width="448" height="448" rx="80" fill="url(#gradient)"/>
  
  <!-- 便签纸效果 -->
  <rect x="80" y="120" width="352" height="280" rx="16" fill="#FFFFFF" stroke="#E5E5EA" stroke-width="2"/>
  <rect x="96" y="136" width="320" height="248" rx="8" fill="#FAFAFA"/>
  
  <!-- 便签内容线条 -->
  <line x1="120" y1="180" x2="368" y2="180" stroke="#007AFF" stroke-width="3" stroke-linecap="round"/>
  <line x1="120" y1="220" x2="320" y2="220" stroke="#8E8E93" stroke-width="2" stroke-linecap="round"/>
  <line x1="120" y1="260" x2="340" y2="260" stroke="#8E8E93" stroke-width="2" stroke-linecap="round"/>
  <line x1="120" y1="300" x2="280" y2="300" stroke="#8E8E93" stroke-width="2" stroke-linecap="round"/>
  
  <!-- 搜索图标 -->
  <circle cx="400" cy="112" r="24" fill="#007AFF"/>
  <circle cx="400" cy="112" r="12" fill="none" stroke="#FFFFFF" stroke-width="2"/>
  <line x1="409" y1="121" x2="416" y2="128" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round"/>
  
  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#007AFF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#5856D6;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
