<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>zNotes - Mac风格桌面便签</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=SF+Pro+Display:wght@300;400;500;600&display=swap" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="window-controls" id="windowControls" style="display: none;">
                    <button class="window-btn minimize-btn" id="minimizeBtn" title="最小化">
                        <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                            <path d="M2 6H10" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                        </svg>
                    </button>
                    <button class="window-btn close-btn" id="closeBtn" title="关闭">
                        <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                            <path d="M9 3L3 9M3 3L9 9" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                        </svg>
                    </button>
                </div>
                <h1 class="app-title">zNotes</h1>
                <div class="header-actions">
                    <button class="theme-toggle-btn" id="themeToggle" title="切换主题">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path d="M8 12C10.2091 12 12 10.2091 12 8C12 5.79086 10.2091 4 8 4C5.79086 4 4 5.79086 4 8C4 10.2091 5.79086 12 8 12Z" stroke="currentColor" stroke-width="1.5"/>
                            <path d="M8 1V3M8 13V15M15 8H13M3 8H1M12.6569 3.34314L11.2426 4.75736M4.75736 11.2426L3.34314 12.6569M12.6569 12.6569L11.2426 11.2426M4.75736 4.75736L3.34314 3.34314" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                        </svg>
                    </button>
                    <button class="new-note-btn" id="newNoteBtn" title="新建便签 (Cmd+N)">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path d="M8 3V13M3 8H13" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                    </button>
                </div>
            </div>
            
            <!-- 搜索框 -->
            <div class="search-container">
                <div class="search-box">
                    <svg class="search-icon" width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path d="M7 12C9.76142 12 12 9.76142 12 7C12 4.23858 9.76142 2 7 2C4.23858 2 2 4.23858 2 7C2 9.76142 4.23858 12 7 12Z" stroke="currentColor" stroke-width="1.5"/>
                        <path d="M14 14L10.5 10.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                    </svg>
                    <input type="text" id="searchInput" placeholder="搜索便签..." autocomplete="off">
                    <button class="clear-search" id="clearSearch" style="display: none;">
                        <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                            <path d="M9 3L3 9M3 3L9 9" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                        </svg>
                    </button>
                </div>
            </div>
            
            <!-- 便签列表 -->
            <div class="notes-list" id="notesList">
                <!-- 便签项目将通过JavaScript动态生成 -->
            </div>
        </div>
        
        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 欢迎界面 -->
            <div class="welcome-screen" id="welcomeScreen">
                <div class="welcome-content">
                    <div class="welcome-icon">
                        <svg width="64" height="64" viewBox="0 0 64 64" fill="none">
                            <rect x="8" y="12" width="48" height="40" rx="4" stroke="currentColor" stroke-width="2" fill="none"/>
                            <path d="M16 24H48M16 32H40M16 40H44" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                    </div>
                    <h2>欢迎使用 zNotes</h2>
                    <p>创建你的第一个便签开始记录想法</p>
                    <button class="welcome-btn" onclick="createNewNote()">创建便签</button>
                </div>
            </div>
            
            <!-- 便签编辑器 -->
            <div class="note-editor" id="noteEditor" style="display: none;">
                <div class="editor-header">
                    <input type="text" class="note-title" id="noteTitle" placeholder="便签标题..." maxlength="100">
                    <div class="editor-actions">
                        <button class="action-btn" id="exportNoteBtn" title="导出便签">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                <path d="M8 1V11M5 8L8 11L11 8M2 13H14" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </button>
                        <button class="action-btn danger" id="deleteNoteBtn" title="删除便签 (Cmd+D)">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                <path d="M6 2H10M2 4H14M12 4V13C12 13.5523 11.5523 14 11 14H5C4.44772 14 4 13.5523 4 13V4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="editor-content">
                    <textarea class="note-content" id="noteContent" placeholder="开始输入你的想法..."></textarea>
                </div>
                <div class="editor-footer">
                    <div class="note-info">
                        <span class="word-count" id="wordCount">0 字</span>
                        <span class="last-modified" id="lastModified"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 确认删除对话框 -->
    <div class="modal-overlay" id="deleteModal" style="display: none;">
        <div class="modal">
            <div class="modal-header">
                <h3>删除便签</h3>
            </div>
            <div class="modal-content">
                <p>确定要删除这个便签吗？此操作无法撤销。</p>
            </div>
            <div class="modal-actions">
                <button class="btn btn-secondary" id="cancelDelete">取消</button>
                <button class="btn btn-danger" id="confirmDelete">删除</button>
            </div>
        </div>
    </div>
    
    <script src="script.js"></script>
</body>
</html>
