const { contextBridge, ipc<PERSON>enderer } = require('electron');

// 安全地暴露API给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
    // 数据存储
    store: {
        get: (key) => ipc<PERSON>enderer.invoke('store-get', key),
        set: (key, value) => ipcRenderer.invoke('store-set', key, value),
        delete: (key) => ipc<PERSON>enderer.invoke('store-delete', key)
    },
    
    // 文件操作
    showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
    
    // 窗口控制
    window: {
        minimize: () => ipcRenderer.invoke('window-minimize'),
        close: () => ipcRenderer.invoke('window-close')
    },
    
    // 监听主进程消息
    on: (channel, callback) => {
        const validChannels = [
            'create-new-note',
            'delete-note',
            'export-note',
            'export-all-notes',
            'focus-search',
            'toggle-theme',
            'show-preferences'
        ];
        
        if (validChannels.includes(channel)) {
            ipcRenderer.on(channel, callback);
        }
    },
    
    // 移除监听器
    removeListener: (channel, callback) => {
        ipcRenderer.removeListener(channel, callback);
    },
    
    // 发送消息到主进程
    send: (channel, data) => {
        const validChannels = [
            'app-ready',
            'note-created',
            'note-updated',
            'note-deleted'
        ];
        
        if (validChannels.includes(channel)) {
            ipcRenderer.send(channel, data);
        }
    },
    
    // 获取平台信息
    platform: process.platform,
    
    // 检查是否为开发模式
    isDev: process.argv.includes('--dev')
});
