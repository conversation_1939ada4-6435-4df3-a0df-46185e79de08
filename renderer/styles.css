/* Mac风格便签应用样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #007AFF;
    --secondary-color: #5856D6;
    --success-color: #34C759;
    --warning-color: #FF9500;
    --danger-color: #FF3B30;
    --background-color: #F2F2F7;
    --surface-color: #FFFFFF;
    --text-primary: #000000;
    --text-secondary: #8E8E93;
    --text-tertiary: #C7C7CC;
    --border-color: #E5E5EA;
    --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
    --border-radius: 8px;
    --border-radius-large: 12px;
    --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 深色模式 */
@media (prefers-color-scheme: dark) {
    :root {
        --background-color: #1C1C1E;
        --surface-color: #2C2C2E;
        --text-primary: #FFFFFF;
        --text-secondary: #8E8E93;
        --text-tertiary: #48484A;
        --border-color: #38383A;
    }
}

[data-theme="dark"] {
    --background-color: #1C1C1E;
    --surface-color: #2C2C2E;
    --text-primary: #FFFFFF;
    --text-secondary: #8E8E93;
    --text-tertiary: #48484A;
    --border-color: #38383A;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.5;
    overflow: hidden;
}

.app-container {
    display: flex;
    height: 100vh;
    background-color: var(--background-color);
}

/* 侧边栏样式 */
.sidebar {
    width: 280px;
    background-color: var(--surface-color);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.sidebar-header {
    padding: 20px 16px 16px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.window-controls {
    position: absolute;
    left: 16px;
    top: 20px;
    display: flex;
    gap: 8px;
    z-index: 10;
}

.window-btn {
    width: 20px;
    height: 20px;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    opacity: 0.7;
}

.window-btn:hover {
    opacity: 1;
}

.minimize-btn {
    background-color: #FFBD2E;
    color: #995700;
}

.close-btn {
    background-color: #FF5F57;
    color: #BF0711;
}

.minimize-btn:hover {
    background-color: #FFB000;
}

.close-btn:hover {
    background-color: #FF3B30;
}

.app-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary);
}

.header-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.theme-toggle-btn {
    width: 32px;
    height: 32px;
    border: none;
    background-color: transparent;
    color: var(--text-secondary);
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.theme-toggle-btn:hover {
    background-color: var(--background-color);
    color: var(--text-primary);
}

.new-note-btn {
    width: 32px;
    height: 32px;
    border: none;
    background-color: var(--primary-color);
    color: white;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.new-note-btn:hover {
    background-color: #0056CC;
    transform: scale(1.05);
}

.new-note-btn:active {
    transform: scale(0.95);
}

/* 搜索框样式 */
.search-container {
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-icon {
    position: absolute;
    left: 12px;
    color: var(--text-secondary);
    z-index: 1;
}

#searchInput {
    width: 100%;
    padding: 10px 12px 10px 36px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--background-color);
    font-size: 14px;
    color: var(--text-primary);
    transition: var(--transition);
}

#searchInput:focus {
    outline: none;
    border-color: var(--primary-color);
    background-color: var(--surface-color);
}

.clear-search {
    position: absolute;
    right: 8px;
    width: 20px;
    height: 20px;
    border: none;
    background: none;
    color: var(--text-secondary);
    cursor: pointer;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.clear-search:hover {
    background-color: var(--border-color);
}

/* 便签列表样式 */
.notes-list {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
}

.note-item {
    padding: 12px;
    margin-bottom: 4px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    border: 1px solid transparent;
    user-select: none;
}

.note-item:hover {
    background-color: var(--background-color);
}

.note-item.active {
    background-color: var(--primary-color);
    color: white;
}

.note-item.active .note-preview {
    color: rgba(255, 255, 255, 0.8);
}

.note-item.active .note-date {
    color: rgba(255, 255, 255, 0.6);
}

.note-item.dragging {
    opacity: 0.5;
    transform: rotate(2deg);
}

.note-item.drag-over {
    border-color: var(--primary-color);
    background-color: rgba(0, 122, 255, 0.1);
}

.note-item-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.note-preview {
    font-size: 13px;
    color: var(--text-secondary);
    margin-bottom: 4px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.note-date {
    font-size: 11px;
    color: var(--text-tertiary);
}

/* 主内容区域样式 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: var(--surface-color);
    overflow: hidden;
}

/* 欢迎界面样式 */
.welcome-screen {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--surface-color);
}

.welcome-content {
    text-align: center;
    max-width: 400px;
    padding: 40px;
}

.welcome-icon {
    margin-bottom: 24px;
    color: var(--text-secondary);
}

.welcome-content h2 {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 12px;
    color: var(--text-primary);
}

.welcome-content p {
    font-size: 16px;
    color: var(--text-secondary);
    margin-bottom: 32px;
}

.welcome-btn {
    padding: 12px 24px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.welcome-btn:hover {
    background-color: #0056CC;
    transform: translateY(-1px);
}

.welcome-btn:active {
    transform: translateY(0);
}

/* 便签编辑器样式 */
.note-editor {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.editor-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.note-title {
    flex: 1;
    font-size: 24px;
    font-weight: 600;
    border: none;
    background: none;
    color: var(--text-primary);
    margin-right: 16px;
}

.note-title:focus {
    outline: none;
}

.note-title::placeholder {
    color: var(--text-tertiary);
}

.editor-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    width: 36px;
    height: 36px;
    border: none;
    background-color: transparent;
    color: var(--text-secondary);
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.action-btn:hover {
    background-color: var(--background-color);
    color: var(--text-primary);
}

.action-btn.danger:hover {
    background-color: var(--danger-color);
    color: white;
}

.editor-content {
    flex: 1;
    padding: 0 24px;
    overflow: hidden;
}

.note-content {
    width: 100%;
    height: 100%;
    border: none;
    background: none;
    font-size: 16px;
    line-height: 1.6;
    color: var(--text-primary);
    resize: none;
    font-family: inherit;
}

.note-content:focus {
    outline: none;
}

.note-content::placeholder {
    color: var(--text-tertiary);
}

.editor-footer {
    padding: 16px 24px;
    border-top: 1px solid var(--border-color);
    background-color: var(--background-color);
}

.note-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: var(--text-secondary);
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(8px);
}

.modal {
    background-color: var(--surface-color);
    border-radius: var(--border-radius-large);
    box-shadow: var(--shadow-medium);
    max-width: 400px;
    width: 90%;
    overflow: hidden;
}

.modal-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.modal-content {
    padding: 16px 24px;
}

.modal-content p {
    color: var(--text-secondary);
    line-height: 1.5;
}

.modal-actions {
    padding: 16px 24px;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.btn-secondary {
    background-color: var(--background-color);
    color: var(--text-primary);
}

.btn-secondary:hover {
    background-color: var(--border-color);
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background-color: #E6342A;
}

/* 滚动条样式 */
.notes-list::-webkit-scrollbar,
.note-content::-webkit-scrollbar {
    width: 6px;
}

.notes-list::-webkit-scrollbar-track,
.note-content::-webkit-scrollbar-track {
    background: transparent;
}

.notes-list::-webkit-scrollbar-thumb,
.note-content::-webkit-scrollbar-thumb {
    background-color: var(--text-tertiary);
    border-radius: 3px;
}

.notes-list::-webkit-scrollbar-thumb:hover,
.note-content::-webkit-scrollbar-thumb:hover {
    background-color: var(--text-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        position: absolute;
        z-index: 100;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .main-content {
        width: 100%;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.note-item {
    animation: fadeIn 0.3s ease;
}

/* 高亮搜索结果 */
.highlight {
    background-color: #FFEB3B;
    color: #000;
    padding: 1px 2px;
    border-radius: 2px;
}
