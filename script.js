// zNotes - Mac风格桌面便签应用
class ZNotes {
    constructor() {
        this.notes = [];
        this.currentNoteId = null;
        this.searchQuery = '';
        this.isAutoSaving = false;
        this.theme = localStorage.getItem('zNotes-theme') || 'auto';

        this.init();
    }
    
    init() {
        this.loadNotes();
        this.initTheme();
        this.bindEvents();
        this.updateUI();
    }
    
    // 生成唯一ID
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
    
    // 加载便签数据
    loadNotes() {
        try {
            const savedNotes = localStorage.getItem('zNotes');
            if (savedNotes) {
                this.notes = JSON.parse(savedNotes);
            }
        } catch (error) {
            console.error('加载便签失败:', error);
            this.notes = [];
        }
    }
    
    // 保存便签数据
    saveNotes() {
        try {
            localStorage.setItem('zNotes', JSON.stringify(this.notes));
        } catch (error) {
            console.error('保存便签失败:', error);
        }
    }

    // 初始化主题
    initTheme() {
        this.applyTheme(this.theme);
    }

    // 应用主题
    applyTheme(theme) {
        const body = document.body;

        if (theme === 'dark') {
            body.setAttribute('data-theme', 'dark');
        } else if (theme === 'light') {
            body.removeAttribute('data-theme');
        } else {
            // auto模式，跟随系统
            body.removeAttribute('data-theme');
        }

        this.theme = theme;
        localStorage.setItem('zNotes-theme', theme);
    }

    // 切换主题
    toggleTheme() {
        const themes = ['auto', 'light', 'dark'];
        const currentIndex = themes.indexOf(this.theme);
        const nextIndex = (currentIndex + 1) % themes.length;
        this.applyTheme(themes[nextIndex]);
    }

    // 导出便签
    exportNote(id) {
        const note = this.getNote(id);
        if (!note) return;

        const content = `# ${note.title}\n\n${note.content}\n\n---\n创建时间: ${new Date(note.createdAt).toLocaleString('zh-CN')}\n修改时间: ${new Date(note.updatedAt).toLocaleString('zh-CN')}`;

        const blob = new Blob([content], { type: 'text/markdown;charset=utf-8' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `${note.title}.md`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    // 导出所有便签
    exportAllNotes() {
        if (this.notes.length === 0) return;

        const content = this.notes.map(note =>
            `# ${note.title}\n\n${note.content}\n\n---\n创建时间: ${new Date(note.createdAt).toLocaleString('zh-CN')}\n修改时间: ${new Date(note.updatedAt).toLocaleString('zh-CN')}\n\n`
        ).join('\n');

        const blob = new Blob([content], { type: 'text/markdown;charset=utf-8' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `zNotes导出_${new Date().toISOString().split('T')[0]}.md`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
    
    // 创建新便签
    createNote(title = '', content = '') {
        const note = {
            id: this.generateId(),
            title: title || '新便签',
            content: content,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        
        this.notes.unshift(note);
        this.saveNotes();
        this.currentNoteId = note.id;
        this.updateUI();
        this.showEditor();
        
        // 聚焦到标题输入框
        setTimeout(() => {
            const titleInput = document.getElementById('noteTitle');
            if (titleInput) {
                titleInput.focus();
                titleInput.select();
            }
        }, 100);
        
        return note;
    }
    
    // 更新便签
    updateNote(id, updates) {
        const noteIndex = this.notes.findIndex(note => note.id === id);
        if (noteIndex !== -1) {
            this.notes[noteIndex] = {
                ...this.notes[noteIndex],
                ...updates,
                updatedAt: new Date().toISOString()
            };
            this.saveNotes();
            this.updateNotesList();
        }
    }
    
    // 删除便签
    deleteNote(id) {
        this.notes = this.notes.filter(note => note.id !== id);
        this.saveNotes();
        
        if (this.currentNoteId === id) {
            this.currentNoteId = null;
            this.showWelcome();
        }
        
        this.updateUI();
    }
    
    // 获取便签
    getNote(id) {
        return this.notes.find(note => note.id === id);
    }
    
    // 搜索便签
    searchNotes(query) {
        this.searchQuery = query.toLowerCase();
        this.updateNotesList();
    }
    
    // 过滤便签
    getFilteredNotes() {
        if (!this.searchQuery) {
            return this.notes;
        }
        
        return this.notes.filter(note => 
            note.title.toLowerCase().includes(this.searchQuery) ||
            note.content.toLowerCase().includes(this.searchQuery)
        );
    }
    
    // 高亮搜索关键词
    highlightText(text, query) {
        if (!query) return text;
        
        const regex = new RegExp(`(${query})`, 'gi');
        return text.replace(regex, '<span class="highlight">$1</span>');
    }
    
    // 格式化日期
    formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diff = now - date;
        
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);
        
        if (minutes < 1) return '刚刚';
        if (minutes < 60) return `${minutes}分钟前`;
        if (hours < 24) return `${hours}小时前`;
        if (days < 7) return `${days}天前`;
        
        return date.toLocaleDateString('zh-CN', {
            month: 'short',
            day: 'numeric'
        });
    }
    
    // 获取便签预览文本
    getPreviewText(content, maxLength = 100) {
        const text = content.replace(/\n/g, ' ').trim();
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    }
    
    // 更新UI
    updateUI() {
        this.updateNotesList();
        this.updateEditor();
        this.updateWordCount();
    }
    
    // 更新便签列表
    updateNotesList() {
        const notesList = document.getElementById('notesList');
        const filteredNotes = this.getFilteredNotes();
        
        if (filteredNotes.length === 0) {
            notesList.innerHTML = `
                <div class="empty-state" style="padding: 40px 16px; text-align: center; color: var(--text-secondary);">
                    ${this.searchQuery ? '没有找到匹配的便签' : '还没有便签'}
                </div>
            `;
            return;
        }
        
        notesList.innerHTML = filteredNotes.map(note => {
            const isActive = note.id === this.currentNoteId;
            const title = this.highlightText(note.title, this.searchQuery);
            const preview = this.highlightText(this.getPreviewText(note.content), this.searchQuery);
            
            return `
                <div class="note-item ${isActive ? 'active' : ''}" data-note-id="${note.id}">
                    <div class="note-item-title">${title}</div>
                    <div class="note-preview">${preview}</div>
                    <div class="note-date">${this.formatDate(note.updatedAt)}</div>
                </div>
            `;
        }).join('');
    }
    
    // 更新编辑器
    updateEditor() {
        const titleInput = document.getElementById('noteTitle');
        const contentTextarea = document.getElementById('noteContent');
        const lastModified = document.getElementById('lastModified');
        
        if (this.currentNoteId) {
            const note = this.getNote(this.currentNoteId);
            if (note) {
                titleInput.value = note.title;
                contentTextarea.value = note.content;
                lastModified.textContent = `最后修改: ${this.formatDate(note.updatedAt)}`;
            }
        }
    }
    
    // 更新字数统计
    updateWordCount() {
        const contentTextarea = document.getElementById('noteContent');
        const wordCount = document.getElementById('wordCount');
        
        if (contentTextarea && wordCount) {
            const count = contentTextarea.value.length;
            wordCount.textContent = `${count} 字`;
        }
    }
    
    // 显示欢迎界面
    showWelcome() {
        document.getElementById('welcomeScreen').style.display = 'flex';
        document.getElementById('noteEditor').style.display = 'none';
    }
    
    // 显示编辑器
    showEditor() {
        document.getElementById('welcomeScreen').style.display = 'none';
        document.getElementById('noteEditor').style.display = 'flex';
    }
    
    // 自动保存
    autoSave() {
        if (this.isAutoSaving || !this.currentNoteId) return;
        
        this.isAutoSaving = true;
        
        setTimeout(() => {
            const titleInput = document.getElementById('noteTitle');
            const contentTextarea = document.getElementById('noteContent');
            
            if (titleInput && contentTextarea) {
                this.updateNote(this.currentNoteId, {
                    title: titleInput.value || '无标题',
                    content: contentTextarea.value
                });
            }
            
            this.isAutoSaving = false;
        }, 500);
    }
    
    // 绑定事件
    bindEvents() {
        // 新建便签按钮
        document.getElementById('newNoteBtn').addEventListener('click', () => {
            this.createNote();
        });

        // 主题切换按钮
        document.getElementById('themeToggle').addEventListener('click', () => {
            this.toggleTheme();
        });
        
        // 搜索功能
        const searchInput = document.getElementById('searchInput');
        const clearSearch = document.getElementById('clearSearch');
        
        searchInput.addEventListener('input', (e) => {
            const query = e.target.value;
            this.searchNotes(query);
            
            clearSearch.style.display = query ? 'flex' : 'none';
        });
        
        clearSearch.addEventListener('click', () => {
            searchInput.value = '';
            this.searchNotes('');
            clearSearch.style.display = 'none';
            searchInput.focus();
        });
        
        // 便签列表点击事件
        document.getElementById('notesList').addEventListener('click', (e) => {
            const noteItem = e.target.closest('.note-item');
            if (noteItem) {
                const noteId = noteItem.dataset.noteId;
                this.currentNoteId = noteId;
                this.updateUI();
                this.showEditor();
            }
        });
        
        // 编辑器事件
        const titleInput = document.getElementById('noteTitle');
        const contentTextarea = document.getElementById('noteContent');
        
        titleInput.addEventListener('input', () => {
            this.autoSave();
        });
        
        contentTextarea.addEventListener('input', () => {
            this.updateWordCount();
            this.autoSave();
        });
        
        // 导出便签
        document.getElementById('exportNoteBtn').addEventListener('click', () => {
            if (this.currentNoteId) {
                this.exportNote(this.currentNoteId);
            }
        });

        // 删除便签
        document.getElementById('deleteNoteBtn').addEventListener('click', () => {
            if (this.currentNoteId) {
                this.showDeleteModal();
            }
        });
        
        // 删除确认对话框
        document.getElementById('cancelDelete').addEventListener('click', () => {
            this.hideDeleteModal();
        });
        
        document.getElementById('confirmDelete').addEventListener('click', () => {
            if (this.currentNoteId) {
                this.deleteNote(this.currentNoteId);
                this.hideDeleteModal();
            }
        });
        
        // 快捷键支持
        document.addEventListener('keydown', (e) => {
            if (e.metaKey || e.ctrlKey) {
                switch (e.key) {
                    case 'n':
                        e.preventDefault();
                        this.createNote();
                        break;
                    case 'd':
                        e.preventDefault();
                        if (this.currentNoteId) {
                            this.showDeleteModal();
                        }
                        break;
                    case 'f':
                        e.preventDefault();
                        searchInput.focus();
                        break;
                }
            }
            
            if (e.key === 'Escape') {
                this.hideDeleteModal();
            }
        });
        
        // 点击模态框外部关闭
        document.getElementById('deleteModal').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) {
                this.hideDeleteModal();
            }
        });
    }
    
    // 显示删除确认对话框
    showDeleteModal() {
        document.getElementById('deleteModal').style.display = 'flex';
    }
    
    // 隐藏删除确认对话框
    hideDeleteModal() {
        document.getElementById('deleteModal').style.display = 'none';
    }
}

// 全局函数
function createNewNote() {
    if (window.zNotes) {
        window.zNotes.createNote();
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.zNotes = new ZNotes();
    
    // 如果没有便签，显示欢迎界面
    if (window.zNotes.notes.length === 0) {
        window.zNotes.showWelcome();
    } else {
        // 如果有便签，选择第一个
        window.zNotes.currentNoteId = window.zNotes.notes[0].id;
        window.zNotes.showEditor();
        window.zNotes.updateUI();
    }
});
