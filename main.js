const { app, BrowserWindow, Menu, Tray, globalShortcut, ipcMain, dialog, shell } = require('electron');
const path = require('path');
const Store = require('electron-store');

// 初始化数据存储
const store = new Store();

class ZNotesApp {
    constructor() {
        this.mainWindow = null;
        this.tray = null;
        this.isQuitting = false;
        
        this.init();
    }
    
    init() {
        // 设置应用程序事件
        app.whenReady().then(() => {
            this.createWindow();
            this.createTray();
            this.createMenu();
            this.registerGlobalShortcuts();
            this.setupIPC();
        });
        
        app.on('window-all-closed', () => {
            if (process.platform !== 'darwin') {
                app.quit();
            }
        });
        
        app.on('activate', () => {
            if (BrowserWindow.getAllWindows().length === 0) {
                this.createWindow();
            } else {
                this.showWindow();
            }
        });
        
        app.on('before-quit', () => {
            this.isQuitting = true;
        });
    }
    
    createWindow() {
        this.mainWindow = new BrowserWindow({
            width: 1000,
            height: 700,
            minWidth: 800,
            minHeight: 600,
            titleBarStyle: 'hiddenInset',
            vibrancy: 'under-window',
            transparent: false,
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                enableRemoteModule: false,
                preload: path.join(__dirname, 'renderer', 'preload.js')
            },
            icon: path.join(__dirname, 'assets', 'icon.png'),
            show: false
        });
        
        // 加载应用页面
        this.mainWindow.loadFile(path.join(__dirname, 'renderer', 'index.html'));
        
        // 窗口事件
        this.mainWindow.once('ready-to-show', () => {
            this.mainWindow.show();
            
            // 开发模式下打开开发者工具
            if (process.argv.includes('--dev')) {
                this.mainWindow.webContents.openDevTools();
            }
        });
        
        this.mainWindow.on('close', (event) => {
            if (!this.isQuitting && process.platform === 'darwin') {
                event.preventDefault();
                this.hideWindow();
            }
        });
        
        this.mainWindow.on('closed', () => {
            this.mainWindow = null;
        });
    }
    
    createTray() {
        const iconPath = path.join(__dirname, 'assets', 'tray-icon.png');
        this.tray = new Tray(iconPath);
        
        const contextMenu = Menu.buildFromTemplate([
            {
                label: '显示 zNotes',
                click: () => this.showWindow()
            },
            {
                label: '新建便签',
                accelerator: 'CmdOrCtrl+N',
                click: () => {
                    this.showWindow();
                    this.mainWindow.webContents.send('create-new-note');
                }
            },
            { type: 'separator' },
            {
                label: '偏好设置',
                click: () => {
                    this.showWindow();
                    this.mainWindow.webContents.send('show-preferences');
                }
            },
            { type: 'separator' },
            {
                label: '退出',
                accelerator: 'CmdOrCtrl+Q',
                click: () => {
                    this.isQuitting = true;
                    app.quit();
                }
            }
        ]);
        
        this.tray.setContextMenu(contextMenu);
        this.tray.setToolTip('zNotes - Mac风格便签');
        
        this.tray.on('click', () => {
            this.toggleWindow();
        });
        
        this.tray.on('double-click', () => {
            this.showWindow();
        });
    }
    
    createMenu() {
        const template = [
            {
                label: 'zNotes',
                submenu: [
                    {
                        label: '关于 zNotes',
                        role: 'about'
                    },
                    { type: 'separator' },
                    {
                        label: '偏好设置...',
                        accelerator: 'CmdOrCtrl+,',
                        click: () => {
                            this.mainWindow.webContents.send('show-preferences');
                        }
                    },
                    { type: 'separator' },
                    {
                        label: '隐藏 zNotes',
                        accelerator: 'CmdOrCtrl+H',
                        role: 'hide'
                    },
                    {
                        label: '隐藏其他',
                        accelerator: 'CmdOrCtrl+Alt+H',
                        role: 'hideothers'
                    },
                    {
                        label: '显示全部',
                        role: 'unhide'
                    },
                    { type: 'separator' },
                    {
                        label: '退出',
                        accelerator: 'CmdOrCtrl+Q',
                        click: () => {
                            this.isQuitting = true;
                            app.quit();
                        }
                    }
                ]
            },
            {
                label: '文件',
                submenu: [
                    {
                        label: '新建便签',
                        accelerator: 'CmdOrCtrl+N',
                        click: () => {
                            this.mainWindow.webContents.send('create-new-note');
                        }
                    },
                    { type: 'separator' },
                    {
                        label: '导出便签...',
                        accelerator: 'CmdOrCtrl+E',
                        click: () => {
                            this.mainWindow.webContents.send('export-note');
                        }
                    },
                    {
                        label: '导出所有便签...',
                        accelerator: 'CmdOrCtrl+Shift+E',
                        click: () => {
                            this.mainWindow.webContents.send('export-all-notes');
                        }
                    },
                    { type: 'separator' },
                    {
                        label: '删除便签',
                        accelerator: 'CmdOrCtrl+D',
                        click: () => {
                            this.mainWindow.webContents.send('delete-note');
                        }
                    }
                ]
            },
            {
                label: '编辑',
                submenu: [
                    { label: '撤销', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
                    { label: '重做', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
                    { type: 'separator' },
                    { label: '剪切', accelerator: 'CmdOrCtrl+X', role: 'cut' },
                    { label: '复制', accelerator: 'CmdOrCtrl+C', role: 'copy' },
                    { label: '粘贴', accelerator: 'CmdOrCtrl+V', role: 'paste' },
                    { label: '全选', accelerator: 'CmdOrCtrl+A', role: 'selectall' },
                    { type: 'separator' },
                    {
                        label: '查找',
                        accelerator: 'CmdOrCtrl+F',
                        click: () => {
                            this.mainWindow.webContents.send('focus-search');
                        }
                    }
                ]
            },
            {
                label: '视图',
                submenu: [
                    {
                        label: '切换主题',
                        accelerator: 'CmdOrCtrl+T',
                        click: () => {
                            this.mainWindow.webContents.send('toggle-theme');
                        }
                    },
                    { type: 'separator' },
                    { label: '重新加载', accelerator: 'CmdOrCtrl+R', role: 'reload' },
                    { label: '强制重新加载', accelerator: 'CmdOrCtrl+Shift+R', role: 'forceReload' },
                    { label: '切换开发者工具', accelerator: 'F12', role: 'toggleDevTools' },
                    { type: 'separator' },
                    { label: '实际大小', accelerator: 'CmdOrCtrl+0', role: 'resetZoom' },
                    { label: '放大', accelerator: 'CmdOrCtrl+Plus', role: 'zoomIn' },
                    { label: '缩小', accelerator: 'CmdOrCtrl+-', role: 'zoomOut' },
                    { type: 'separator' },
                    { label: '切换全屏', accelerator: 'Ctrl+Cmd+F', role: 'togglefullscreen' }
                ]
            },
            {
                label: '窗口',
                submenu: [
                    { label: '最小化', accelerator: 'CmdOrCtrl+M', role: 'minimize' },
                    { label: '关闭', accelerator: 'CmdOrCtrl+W', role: 'close' },
                    { type: 'separator' },
                    { label: '前置所有窗口', role: 'front' }
                ]
            },
            {
                label: '帮助',
                submenu: [
                    {
                        label: '了解更多',
                        click: () => {
                            shell.openExternal('https://github.com/znotes/znotes');
                        }
                    }
                ]
            }
        ];
        
        const menu = Menu.buildFromTemplate(template);
        Menu.setApplicationMenu(menu);
    }
    
    registerGlobalShortcuts() {
        // 全局快捷键：快速显示/隐藏应用
        globalShortcut.register('CmdOrCtrl+Shift+N', () => {
            this.toggleWindow();
        });
        
        // 全局快捷键：快速创建新便签
        globalShortcut.register('CmdOrCtrl+Alt+N', () => {
            this.showWindow();
            this.mainWindow.webContents.send('create-new-note');
        });
    }
    
    setupIPC() {
        // 数据存储相关
        ipcMain.handle('store-get', (event, key) => {
            return store.get(key);
        });
        
        ipcMain.handle('store-set', (event, key, value) => {
            store.set(key, value);
        });
        
        ipcMain.handle('store-delete', (event, key) => {
            store.delete(key);
        });
        
        // 文件对话框
        ipcMain.handle('show-save-dialog', async (event, options) => {
            const result = await dialog.showSaveDialog(this.mainWindow, options);
            return result;
        });
        
        // 窗口控制
        ipcMain.handle('window-minimize', () => {
            this.mainWindow.minimize();
        });
        
        ipcMain.handle('window-close', () => {
            this.hideWindow();
        });
    }
    
    showWindow() {
        if (this.mainWindow) {
            if (this.mainWindow.isMinimized()) {
                this.mainWindow.restore();
            }
            this.mainWindow.show();
            this.mainWindow.focus();
        }
    }
    
    hideWindow() {
        if (this.mainWindow) {
            this.mainWindow.hide();
        }
    }
    
    toggleWindow() {
        if (this.mainWindow) {
            if (this.mainWindow.isVisible()) {
                this.hideWindow();
            } else {
                this.showWindow();
            }
        }
    }
}

// 创建应用实例
new ZNotesApp();
